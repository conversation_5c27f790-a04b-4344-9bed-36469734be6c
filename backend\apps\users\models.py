from django.contrib.auth.models import AbstractUser
from django.db import models
from django.utils import timezone


class User(AbstractUser):
    """Custom user model with additional fields for subscription management."""
    
    email = models.EmailField(unique=True)
    first_name = models.CharField(max_length=30)
    last_name = models.CharField(max_length=30)
    phone_number = models.CharField(max_length=20, blank=True, null=True)
    telegram_username = models.Char<PERSON>ield(max_length=100, blank=True, null=True)
    telegram_user_id = models.Char<PERSON>ield(max_length=50, blank=True, null=True)
    
    # Subscription fields
    is_premium = models.BooleanField(default=False)
    subscription_start_date = models.DateTimeField(blank=True, null=True)
    subscription_end_date = models.DateTimeField(blank=True, null=True)
    stripe_customer_id = models.CharField(max_length=100, blank=True, null=True)
    stripe_subscription_id = models.Char<PERSON>ield(max_length=100, blank=True, null=True)
    
    # Profile fields
    profile_picture = models.ImageField(upload_to='profile_pictures/', blank=True, null=True)
    bio = models.TextField(blank=True, null=True)
    date_joined = models.DateTimeField(default=timezone.now)
    last_login = models.DateTimeField(blank=True, null=True)
    
    USERNAME_FIELD = 'email'
    REQUIRED_FIELDS = ['username', 'first_name', 'last_name']
    
    class Meta:
        db_table = 'users'
        verbose_name = 'User'
        verbose_name_plural = 'Users'
    
    def __str__(self):
        return f"{self.first_name} {self.last_name} ({self.email})"
    
    @property
    def full_name(self):
        return f"{self.first_name} {self.last_name}"
    
    @property
    def is_subscription_active(self):
        """Check if user's subscription is currently active."""
        if not self.is_premium or not self.subscription_end_date:
            return False
        return timezone.now() < self.subscription_end_date
    
    def activate_subscription(self, duration_days=30):
        """Activate premium subscription for specified duration."""
        self.is_premium = True
        self.subscription_start_date = timezone.now()
        self.subscription_end_date = timezone.now() + timezone.timedelta(days=duration_days)
        self.save()
    
    def deactivate_subscription(self):
        """Deactivate premium subscription."""
        self.is_premium = False
        self.subscription_end_date = timezone.now()
        self.save()


class UserProfile(models.Model):
    """Extended user profile information."""
    
    user = models.OneToOneField(User, on_delete=models.CASCADE, related_name='profile')
    trading_experience = models.CharField(
        max_length=20,
        choices=[
            ('beginner', 'Beginner'),
            ('intermediate', 'Intermediate'),
            ('advanced', 'Advanced'),
            ('expert', 'Expert'),
        ],
        default='beginner'
    )
    preferred_markets = models.JSONField(default=list, blank=True)  # ['forex', 'crypto', 'stocks']
    timezone = models.CharField(max_length=50, default='UTC')
    notification_preferences = models.JSONField(default=dict, blank=True)
    
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        db_table = 'user_profiles'
        verbose_name = 'User Profile'
        verbose_name_plural = 'User Profiles'
    
    def __str__(self):
        return f"{self.user.full_name}'s Profile"
